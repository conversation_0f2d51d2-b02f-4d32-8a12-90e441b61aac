export const reservationData = {
    arrival_airline: '',
    arrival_flight_number: '',
    cellphone: '',
    departure_airline: '',
    departure_date: '',
    departure_time: '',
    departure_flight_number: '',
    departure_flight_time: '',
    departure_pickup_time_hotel:'',
    destination_id: '',
    destination_location: '',
    email: '',
    extra_service: [
        {
          id:1,
          name:"Stop at the Supermarket or Grocery Store",
          time: '30 minutes maximum.',
          price: 20
        },
        {
          id:2,
          name:"Stop at the Supermarket or Grocery Store",
          time: '31 to 60 minutes maximum.',
          price: 45
        },
        {
          id:3,
          name:"Stop at the Supermarket or Grocery Store",
          time: '61 to 90 minutes maximum.',
          price: 75
        },
    ],
    extra_service_selected: {},
    final_rate: 0,
    fullname: '',
    member_id: '',
    observations: '',
    passenger_number: 0,
    pickup_date: '',
    pickup_time: '',
    pickup_id: '',
    pickup_location: '',
    has_promotion: true,
    promotion:15, // percent
    promotion_type:'',
    rate_regular:0,
    // rate_stop_super_store:30,
    rate_promotion:0,
    is_stop_at_store:false,
    tarifa_base:0,
    trip_type: 'One Way',
    total_passengers: 1,
    unit_id: '',
    unit: ''
    }