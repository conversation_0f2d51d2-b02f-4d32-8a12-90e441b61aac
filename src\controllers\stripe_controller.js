require("dotenv").config();

if( process.env.ENVIRONMENT === 'DEVELOPMENT') {
  // DEVELOPMENT KEY api key Jenson Booking stripe testing
  stripe = require("stripe")(
    process.env.SK_TEST_BTT_STRIPE,
    {
        apiVersion: "2020-08-27",
    }
  );
}
else {
  // PRODUCTION KEY api key transroute stripe
  stripe = require("stripe")(
    process.env.SK_LIVE_BTT_STRIPE,
    {
      apiVersion: "2020-08-27",
    }
   );
}
exports.paymentApiStripe = async (req, res) => {

    //res.status(200).json({message: 'ok stripe', data: req.body});

    const { id, amount, description  } = req.body;

    try {
      const payment = await stripe.paymentIntents.create({
        amount,
        currency: "USD",
        description,
        payment_method: id,
        confirm: true, //confirm the payment at the same time
      });

      // console.log(payment);

      return res.status(200).json({ valid: true, message: "Successful Payment", payment });

    } catch (error) {

      console.log(error);
      return res.json({ message: error.raw.message });
    }

}