{"name": "booking-rci", "version": "0.2.1", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@paypal/react-paypal-js": "^8.1.4", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^1.11.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "axios": "^0.21.1", "bootstrap": "^4.6.0", "font-awesome": "^4.7.0", "moment": "^2.29.1", "react": "^16.14.0", "react-datepicker": "^4.16.0", "react-dom": "^16.14.0", "react-helmet": "^6.1.0", "react-loading-overlay": "^1.0.1", "react-range-picker": "^1.0.1", "react-router-dom": "^5.2.0", "react-scripts": "5.0.1", "react-select": "^3.2.0", "reactstrap": "^8.9.0", "sass": "^1.62.1", "sass-loader": "^13.2.2", "sweetalert2": "^10.14.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}