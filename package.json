{"name": "admin", "version": "0.1.0", "private": true, "dependencies": {"@ashvin27/react-datatable": "^1.5.3", "@fortawesome/fontawesome-svg-core": "^1.2.27", "@fortawesome/free-brands-svg-icons": "^5.12.1", "@fortawesome/free-solid-svg-icons": "^5.12.1", "@fortawesome/react-fontawesome": "^0.1.8", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "axios": "^0.21.1", "bootstrap": "^4.5.2", "bootstrap-daterangepicker": "^3.1.0", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "jquery": "^3.5.1", "moment": "^2.29.1", "react": "^16.13.1", "react-bootstrap-daterangepicker": "^7.0.0", "react-datepicker": "^2.14.0", "react-dom": "^16.13.1", "react-loading-overlay": "^1.0.1", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.1", "react-select": "^3.1.0", "reactstrap": "^8.5.1", "sass": "^1.53.0", "sass-loader": "^8.0.0", "sweetalert2": "^10.0.1", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}