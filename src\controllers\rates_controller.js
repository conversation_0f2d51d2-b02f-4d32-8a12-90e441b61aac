var { createConnection } = require('../database');

var utils = require('./../utils');

const _TABLE = 'rates';
const _ID = 'id_rate'; 

const response = {
    status: 200,
    results: null
}

exports.get = (req, res) => {
    
    if (req.query.origin && req.query.id_unit && req.query.destination) {
        sql = `SELECT * FROM ${_TABLE} WHERE id_unit = ${req.query.id_unit} AND origin = ${req.query.origin} AND destination = ${req.query.destination};`;
    }else{
        sql = `SELECT * from ${_TABLE} `;
    }

    createConnection().query(sql, (error, result) => {
        if (error) throw error;
        res.status(200).json(result);
    });
}

exports.getById = (req, res) => {
    sql = `SELECT * from ${ _TABLE } WHERE ${ _ID } = ${ req.params.id }`;
    createConnection().query(sql, (error, result) => {
        if (error) throw error;
        res.status(200).json(result);
    });
}

exports.insert = (req, res) => {
    
    try {
        utils.functionValidate(req.body, (resp)=>{
            if(resp){
                sql = `INSERT INTO ${ _TABLE } SET ?`;
                createConnection().query(sql, req.body, (err, result) => {
                    let status, _res;
                    if (err) {
                        status = 500;
                        _res = err;
                    }else{
                        status = 200;
                        _res = result;
                    }
                    res.status(status).json(_res);
                });
            }else{
                res.status(405).json({message: "Missing Parameters"});
            }
        });
        
    } catch (e) {
        response.status = 500;
        res.status(200).json(response);
    }
}

exports.update = (req, res) =>{
    try {
        utils.functionValidate(req.body, (resp)=>{

            if(resp){
                sql = `UPDATE ${ _TABLE } SET `;
                let data = [];
                let aux = 0;

                Object.keys(req.body).forEach( (e) => {
                    sql += (aux > 0) ? ',' + e + '= ?' : e + '= ?';
                    aux++;
                    data.push(req.body[e]);
                });

                sql += ` WHERE ${ _ID } = ? `;
                data.push(req.params.id);

                createConnection().query(sql, data,(err, result) => {
                    let status, _res;
                    if (err) {
                        status = 500;
                        _res = err;
                    }else{
                        status = 200;
                        _res = result;
                    }
                    res.status(status).json(_res);
                });
            }else{
                res.status(405).json({message: "Missing Parameters"});
            }
        });

    } catch (error) {
        response.status = 500;
        res.status(200).json(response);
    }
}

exports._delete = (req, res) =>{
    try {
        sql = `DELETE FROM ${ _TABLE }  WHERE  ${ _ID } = ?`;
        let data = [
            req.params.id
        ];

        createConnection().query(sql, data, (err, result) =>{
            let status, _res;
            if (err) {
                status = 500;
                _res = err;
            }else{
                status = 200;
                _res = result;
            }
            res.status(status).json(_res);
        });

    } catch (error) {
        res.status(500).json({message: status});
    }
}
