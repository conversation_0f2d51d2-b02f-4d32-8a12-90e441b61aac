import React, { useContext, useEffect, useState } from 'react'
import { useHistory } from "react-router-dom";
import { Container , Row, Col, Nav, NavItem, NavLink, TabContent, TabPane } from 'reactstrap';
import { Helmet } from "react-helmet";
import { useParams } from 'react-router-dom';
import Summary from './summary'
import classnames from 'classnames';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PaymentStripe from '../booking/payment-stripe';
import { parserEmailData, sendMail } from '../../api_controllers/email_controller';
import { getReservationByFolio, putReservation } from '../../api_controllers/reservations_controller';
import Swal from 'sweetalert2';
import { ReservationContext } from '../../context/reservation.context';
import SummaryPrice from '../booking/summary-price';

export default function CheckoutPage() {
    let history = useHistory();
    const { id } = useParams();
    const { lang } = useContext(ReservationContext);
    const [data, setData] = useState({})
    const [activeTab, setActiveTab] = useState("1");
    // const [paymentID, setPaymentID] = useState('');
    // console.log(id);
    // const validataIfPayment = (payment_id, id) => {
    //     console.log(payment_id);
    //     if(payment_id !== 'Pendiente'){
    //         history.push({ pathname: `/confirmation/${id}`});
    //         window.location.reload();
    //     }
    // }
    useEffect(() => {
        getReservationByFolio(id)
            .then((response) => {
                console.log({response})
                if (response && response.data && response.data.length > 0) {
                    setData(response.data[0]);
                } else {
                    console.error("No reservation data received");
                }
            })
            .catch((error) => {
                console.error("Error fetching reservation:", error);
            });
    }, [id]);

    // useEffect(() => {
    //     validataIfPayment(paymentID, id)
    // }, [paymentID, id]);

    const toggle = (tab) => {
        if (activeTab !== tab) setActiveTab(tab);
    };

    // console.log(data);
    // hadlePayNow metodo para ejecutar el pago y actualizar el registro
    const handlePayNow = (id) => {
        let dataToSend = {
            folio: data.folio,
            payment_id: id,
            trip_type: data.trip_type,
            unit: data.unit,
            pickup_location: data.pickup_location,
            destination_location: data.destination_location,
            total_passengers: data.total_passengers,
            fullname: data.fullname,
            member_id: data.member_id,
            email: data.email,
            cellphone: data.cellphone,
            arrival_datetime: data.arrival_datetime ? data.arrival_datetime : null,
            arrival_airline: data.arrival_airline ? data.arrival_airline : null,
            arrival_flight_number: data.arrival_flight_number ? data.arrival_flight_number : null,
            departure_datetime: data.departure_datetime ? data.departure_datetime : null,
            departure_airline: data.departure_airline ? data.departure_airline : null,
            departure_flight_number: data.departure_flight_number ? data.departure_flight_number : null,
            hotel_departure_time: data.hotel_departure_time ? data.hotel_departure_time : null,
            extra_service: Object.keys(data?.extra_service || {}).length > 0 ? JSON.stringify(data?.extra_service) : null,
            observations: data.observations ? data.observations : null,
            payment_method: data.payment_method,
            discount_code: data.discount_code,
            discount_percent: data.discount_percent,
            total_payment: data.total_payment,
            active: data.active,
        };
        console.log(dataToSend);
        putReservation(dataToSend, data.id_reservation) //update reservation
            .then((res) => {
                let email_data = parserEmailData(dataToSend);
                console.log(email_data);
                sendMail(email_data).then((_response) => {
                    let resp = _response;
                    console.log(resp);
                    Swal.fire("", "The reservations was created successfully.", "success").then(() => {
                        history.push({ pathname: `/confirmation/${dataToSend.folio}`, state: {data, email_data} });
                        localStorage.removeItem("desc");
                        window.location.reload();
                    });
                });
                Swal.fire("", "The reservations was created successfully.", "success").then(() => {
                    history.push({ pathname: `/confirmation/${dataToSend.folio}`, state: {data} });
                    localStorage.removeItem("desc");
                    window.location.reload();
                });
            })
            .catch((e) => {
                console.log("Error:1:", e);
                Swal.fire("Oops!", "Something went wrong when creating reservation... try again later.", "error");
            }
        );
    };
    console.log(data);
    return (
        <>
            <Helmet>
                <meta charSet="utf-8" />
                <title>Checkout Page - RCI Transroute Transpotation</title>
                <link rel="canonical" href="https://booking-laislatour.transroute.com.mx/checkout" />
            </Helmet>
            <Container className='checkout-page'>
                <Row className="my-3">
                    <Col xs={12} md={6}>
                        <h1>Checkout Page</h1>
                        <h4 style={{color: '#000000', fontWeight:600}}>Reservation ID: {data?.folio}<br /></h4>
                    </Col>
                    <Col xs={12} md={6} className="d-flex justify-content-end align-items-end">
                        <h4 style={{color: '#000000', fontWeight:600}}>RCI Member ID: <span style={{color: "#b00505"}}> {data?.member_id}</span><br /></h4>
                    </Col>
                </Row>
                <Row className="data-information">
                    <Col xs={12} md={4}>
                        <Summary data={data} title="Checkout Reservation" lang={lang} />
                    </Col>
                    <Col xs={12} md={8}>
                        <h2 style={{color:'#000000'}}>Payment Method</h2>
                         <h3 className="my-3">
                            </h3>
                        <Nav tabs>
                            <NavItem>
                                <NavLink
                                    className={classnames({ active: activeTab === '1' })}
                                    onClick={() => { toggle('1'); }}
                                >
                                    <FontAwesomeIcon icon="credit-card" /><br />
                                    VISA / MASTER CARD
                                </NavLink>
                            </NavItem>
                            <NavItem>
                                <NavLink
                                    className={classnames({ active: activeTab === '2' })}
                                    onClick={() => { toggle('2'); }}
                                >
                                    <FontAwesomeIcon icon="credit-card" /><br />
                                    REQUEST PAY AMEX CARD
                                </NavLink>
                            </NavItem>
                        </Nav>
                        <TabContent activeTab={activeTab}>
                            <TabPane tabId="1">
                                <Row>
                                    <Col xs="12">
                                        <div className={activeTab === 1 ? 'pymnt-itm active' : 'pymnt-itm'}>

                                            <div className="pymnt-cntnt">
                                                <SummaryPrice
                                                    reserve={data}
                                                    isServiceActive={data.extra_service ? true : false}
                                                    totalPayment={data?.total_payment}
                                                    serviceSelected={data.extra_service ? (typeof data.extra_service === "string" ? JSON.parse(data.extra_service) : data.extra_service) : null}
                                                />
                                                <PaymentStripe folio={data?.folio} totalPayment={data?.total_payment} lang={'eng'} handlePayNow={handlePayNow} />
                                            </div>
                                        </div>
                                    </Col>
                                </Row>
                            </TabPane>
                            <TabPane tabId="2">
                                <Row>
                                    <Col xs="12">
                                        <div className={activeTab === 2 ? 'pymnt-itm active' : 'pymnt-itm'}>
                                            <div className="pymnt-cntnt" style={{minHeight:'100px', display:'flex', justifyContent:'center', alignItems:'center', flexDirection:'column'}}>
                                                <SummaryPrice
                                                    reserve={data}
                                                    isServiceActive={data.extra_service ? true : false}
                                                    totalPayment={data?.total_payment}
                                                    serviceSelected={data.extra_service ? (typeof data.extra_service === "string" ? JSON.parse(data.extra_service) : data.extra_service) : null}
                                                />
                                                <a
                                                    style={{
                                                        backgroundColor:'#25d366',
                                                        padding:'1rem',
                                                        color:'white',
                                                        textDecoration:'none',
                                                        borderRadius:'1rem',
                                                        marginTop:'2rem'
                                                    }}
                                                    target='_blank'
                                                    href={`https://wa.me/6241259657?text=I+Need+to+Pay+with+Amex+Card+Please,+Transportation+for+RCI-Transroute+ID:+${id}&type=phone_number&app_absent=0`}>
                                                        Click for Request Pay with American Express
                                                </a>
                                            </div>
                                        </div>
                                    </Col>
                                </Row>
                            </TabPane>
                        </TabContent>
                        {/* <Alert color="warning">
                            If you wish to pay with a debit or credit card, please click on the dark gray button and scroll/touch down to view all fields and the Pay Now button, please
                        </Alert>
                        <iframe src={`${configs.URL_PAYMENT_PAYPAL}/?paypal=true&description=${localStorage.getItem('desc')}&idReservation=${id}&rate=${data?.total_payment}&project=isla`} frameBorder="0" style={{minHeight:'750px', overflowY:'auto', width:'100%'}}/> */}
                    </Col>

                </Row>
            </Container>
        </>
    )
}