require("dotenv").config();
const nodemailer = require('nodemailer');
const { email_view } = require('../views/email_confirmation');

const getSmtp = () => {
    if(process.env.ENVIRONMENT === 'DEVELOPMENT'){
        var smtpTransport = nodemailer.createTransport({
            host: 'smtp-relay.brevo.com',
            port: 587,
            secure: false,
            auth: {
                user: '<EMAIL>',
                pass: 'bhXDcq1Mt2PxHUvw'
            }
        });
        return smtpTransport;
    } else {
        var smtpTransport = nodemailer.createTransport({
            host: 'transroute.com.mx',
            port: 587,
            secure: false,
            auth: {
                user: '<EMAIL>',
                pass: '!%greFW8){AL'
            }
        });
        return smtpTransport;
    }
}

exports.sendmail = function sendemail(req, res) {

    if (req.body.email) {

        // console.log(req.body);

        let mailOptions;
        if(process.env.ENVIRONMENT === 'DEVELOPMENT'){
            mailOptions = {
                from: ' RCI Transroute <<EMAIL>>',
                to: req.body.email,
                bcc: '<EMAIL>,<EMAIL>',
                subject: 'RCI Dev Transroute',
                html: email_view(req.body.reservation),
            }
        } else {
            mailOptions = {
                from: ' RCI Transroute <<EMAIL>>',
                to: req.body.email,
                bcc: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>',
                subject: 'RCI Transroute Reservation',
                html: email_view(req.body.reservation),
            }
        }

        if (req.body.cc !== null) {
            mailOptions.cc = req.body.cc;
        }

        getSmtp().sendMail(mailOptions, function (error, response) {
            if (error) {
                res.status(500).jsonp({ message: `${error}` });
            } else {
                res.status(200).jsonp({ message: `email was sended` });
            }
        });

    } else {
        // console.log(res);
        res.status(405).jsonp({ send: false });
    }
}