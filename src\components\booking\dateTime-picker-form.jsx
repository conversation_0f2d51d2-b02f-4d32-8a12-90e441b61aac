import { useContext, useEffect, useState } from 'react';
import DatePicker, { registerLocale } from "react-datepicker";
import { FormGroup, InputGroup, InputGroupAddon, InputGroupText, Label, Row } from 'reactstrap'
import { ReservationContext } from '../../context/reservation.context';
import es from 'date-fns/locale/es';
import en from 'date-fns/locale/en-US';
import moment from 'moment';
registerLocale("es", es);
registerLocale("en", en);

export const DateTimePickerForm = ({form, setForm, tripValidator, isRoundTrip, isOneWayAirportDestination, validationErrors, setValidationErrors, formSubmitted}) => {

    const { lang } = useContext(ReservationContext);
    const { trip_type } = form;

    // Inicializar estados con valores del formulario o valores por defecto
    const [pickupDate, setPickupDate] = useState(form.pickup_date ? new Date(form.pickup_date) : null);
    const [pickupTime, setPickupTime] = useState(form.pickup_time ? new Date(`01/01/2023 ${form.pickup_time}`) : null);
    const [minArrivalDateToSelect, setMinArrivalDateToSelect] = useState(new Date());
    const [departureDate, setDepartureDate] = useState(form.departure_date ? new Date(form.departure_date) : null);
    const [departureFlightTime, setDepartureFlightTime] = useState(
        form.departure_flight_time ? new Date(`01/01/2023 ${form.departure_flight_time}`) : null
    );
    const [minDepartureDateToSelect, setMinDepartureDateToSelect] = useState(new Date());

    ////==========================/ arrival methods
    const handlePickupDate = (date) => {
        if (!date) return;

        setPickupDate(date);
        setDepartureDate(null);

        setForm({
            ...form,
            pickup_date: moment(date).format("MMMM DD YYYY"),
            departure_date: ''
        });

        // Clear validation error for pickup date
        setValidationErrors(prev => ({
            ...prev,
            pickup_date: false
        }));
    }

    const handlePickupTime = (time) => {
        if (!time) return;

        setPickupTime(time);
        setForm({
            ...form,
            pickup_time: moment(time).format("h:mm A"),
        });

        // Clear validation error for pickup time
        setValidationErrors(prev => ({
            ...prev,
            pickup_time: false
        }));
    }

    const getMinimalArrivalDateToSelect = () => {
        const minDate = new Date();
        minDate.setDate(minDate.getDate() + 1);
        setMinArrivalDateToSelect(minDate);
    }

    useEffect(() => {
        getMinimalArrivalDateToSelect();
    }, []);

    ////==========================/ departure methods
    const handleDepatureDate = (date) => {
        if (!date) return;

        setDepartureDate(date);
        setForm({
            ...form,
            departure_date: moment(date).format("MMMM DD YYYY")
        });

        // Clear validation error for departure date
        setValidationErrors(prev => ({
            ...prev,
            departure_date: false
        }));
    }

    const handleDepartureFlightTime = (time) => {
        if (!time) return;

        setDepartureFlightTime(time);

        // Calcular la hora de recogida en el hotel (3 horas antes)
        const timeHotelPickup = new Date(time);
        timeHotelPickup.setHours(timeHotelPickup.getHours() - 3);

        setForm({
            ...form,
            departure_flight_time: moment(time).format("h:mm A"),
            departure_pickup_time_hotel: moment(timeHotelPickup).format("h:mm A")
        });

        // Clear validation error for departure flight time
        setValidationErrors(prev => ({
            ...prev,
            departure_flight_time: false
        }));
    }

    const getMinimalDepartureDateToSelect = (date) => {
        if (!date) return;

        const minDate = new Date(date);
        minDate.setDate(minDate.getDate() + 1);
        setMinDepartureDateToSelect(minDate);
    }

    useEffect(() => {
        if (trip_type === 'Round Trip' && pickupDate) {
            getMinimalDepartureDateToSelect(pickupDate);
        }
    }, [trip_type, pickupDate]);

    //////////////////////////////////////////////

  return (
    <>
    <Row className="px-0">
        {
            !tripValidator && (<>
                <FormGroup className="pickupDate col-12 col-lg-6">
                    <Label>{lang === 'eng' ? 'Pickup Date:' : 'Fecha de recogida'}</Label>
                    <InputGroup className="tr-input">
                        <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                                <i className="fa fa-calendar" aria-hidden="true"></i>
                            </InputGroupText>
                        </InputGroupAddon>
                        <DatePicker
                            locale={lang === 'eng' ? 'en' : 'es'}
                            selected={pickupDate}
                            onChange={handlePickupDate}
                            className={`tr-datepicker ${formSubmitted && validationErrors?.pickup_date ? 'is-invalid' : ''}`}
                            dateFormat="MM/dd/yyyy"
                            minDate={minArrivalDateToSelect}
                            placeholderText={lang === 'eng' ? 'Select date' : 'Seleccionar fecha'}
                        />
                    </InputGroup>
                    {formSubmitted && validationErrors?.pickup_date && (
                        <div className="invalid-feedback" style={{display: 'block'}}>
                            {lang === 'eng' ? 'Please select a pickup date' : 'Por favor seleccione una fecha de recogida'}
                        </div>
                    )}
                </FormGroup>
                <FormGroup className="pickupTime col-12 col-lg-6">
                    <Label>{lang === 'eng' ? 'Pickup Time:' : 'Hora de recogida'}</Label>
                    <InputGroup className="tr-input">
                        <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                                <i className="fa fa-clock-o" aria-hidden="true"></i>
                            </InputGroupText>
                        </InputGroupAddon>
                        <DatePicker
                            locale={lang === 'eng' ? 'en' : 'es'}
                            selected={pickupTime}
                            onChange={handlePickupTime}
                            className={`tr-datepicker ${formSubmitted && validationErrors?.pickup_time ? 'is-invalid' : ''}`}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={5}
                            dateFormat="h:mm aa"
                            placeholderText={lang === 'eng' ? 'Select time' : 'Seleccionar hora'}
                        />
                    </InputGroup>
                    {formSubmitted && validationErrors?.pickup_time && (
                        <div className="invalid-feedback" style={{display: 'block'}}>
                            {lang === 'eng' ? 'Please select a pickup time' : 'Por favor seleccione una hora de recogida'}
                        </div>
                    )}
                </FormGroup>
                </>
            )
        }
        {
            (form.trip_type === 'Round Trip' || isOneWayAirportDestination) && (
                <>
                <FormGroup className="departureDate col-12 col-lg-6">
                    <Label>
                        {lang === 'eng' ? 'Departure Date:' : 'Fecha partida'}
                        {!isOneWayAirportDestination && !pickupDate && (
                            <><br/><small style={{color:'tomato'}}>
                                {lang === 'eng' ? 'Select Pickup Date first' : 'Seleccione fecha de recogida primero'}
                            </small></>
                        )}
                    </Label>
                    <InputGroup className="tr-input">
                        <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                                <i className="fa fa-calendar" aria-hidden="true"></i>
                            </InputGroupText>
                        </InputGroupAddon>
                        <DatePicker
                            locale={lang === 'eng' ? 'en' : 'es'}
                            selected={departureDate}
                            onChange={handleDepatureDate}
                            className={`tr-datepicker ${formSubmitted && validationErrors?.departure_date ? 'is-invalid' : ''}`}
                            dateFormat="MM/dd/yyyy"
                            minDate={isOneWayAirportDestination ? minArrivalDateToSelect : minDepartureDateToSelect}
                            disabled={isOneWayAirportDestination ? false : !pickupDate}
                            placeholderText={lang === 'eng' ? 'Select date' : 'Seleccionar fecha'}
                        />
                    </InputGroup>
                    {formSubmitted && validationErrors?.departure_date && (
                        <div className="invalid-feedback" style={{display: 'block'}}>
                            {lang === 'eng' ? 'Please select a departure date' : 'Por favor seleccione una fecha de partida'}
                        </div>
                    )}
                </FormGroup>
                <FormGroup className="departureFlightTime col-12 col-lg-6">
                    <Label>
                        {lang === 'eng' ? 'Departure Flight Time:' : 'Hora de partida del vuelo'}
                        {!isOneWayAirportDestination && !pickupDate && (
                            <><br/><small style={{color:'tomato'}}>
                                {lang === 'eng' ? 'Select Pickup Date first' : 'Seleccione fecha de recogida primero'}
                            </small></>
                        )}
                    </Label>
                    <InputGroup className="tr-input">
                        <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                                <i className="fa fa-clock-o" aria-hidden="true"></i>
                            </InputGroupText>
                        </InputGroupAddon>
                        <DatePicker
                            locale={lang === 'eng' ? 'en' : 'es'}
                            selected={departureFlightTime}
                            onChange={handleDepartureFlightTime}
                            className={`tr-datepicker ${formSubmitted && validationErrors?.departure_flight_time ? 'is-invalid' : ''}`}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={5}
                            dateFormat="h:mm aa"
                            disabled={isOneWayAirportDestination ? false : !pickupDate}
                            placeholderText={lang === 'eng' ? 'Select time' : 'Seleccionar hora'}
                        />
                    </InputGroup>
                    {formSubmitted && validationErrors?.departure_flight_time && (
                        <div className="invalid-feedback" style={{display: 'block'}}>
                            {lang === 'eng' ? 'Please select a departure time' : 'Por favor seleccione una hora de partida'}
                        </div>
                    )}
                </FormGroup>

                </>
            )
        }
    </Row>

    </>
  )
}
