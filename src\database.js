let mysql = require('mysql2');

exports.createConnection = function(){
    // Seleccionar configuración basada en el entorno
    let host, user, password, database;
    if(process.env.ENVIRONMENT === 'DEVELOPMENT'){
        host = process.env.DEV_DB_HOST;
        user = process.env.DEV_DB_USER;
        password = process.env.DEV_DB_PASS;
        database = process.env.DEV_DB_NAME;
    } else {
        host = process.env.DB_HOST;
        user = process.env.DB_USER;
        password = process.env.DB_PASS;
        database = process.env.DB_NAME;
    }
  
    let client = mysql.createConnection({
      host: host,
      user: user,
      password: password,
      database: database
    });
  
    return client;
};