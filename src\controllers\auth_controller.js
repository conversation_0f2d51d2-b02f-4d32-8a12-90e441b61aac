
const jwt = require('jsonwebtoken');
const _key = require('./../utils');

var { createConnection } = require('../database');
var conn = createConnection();

/* STATUS*/
// 200 - Success = No Error Request
// 500 - Error = Catch Response
// 405 - partial error = Missing Parameters
// 404 - Not found
// 410 - partion error => Login Failed

//login POST Request
exports.login = function login(req, res) {

    try {
        sql = "SELECT * FROM users WHERE email ='" + req.body.email + "' AND password = MD5('" + req.body.password + "') and status = 1";

        conn.query(sql, function (error, results) {

            if (error) throw error;

            if (results.length > 0){

                const _token = jwt.sign({ id: results[0].id_user,id_rol: results[0].id_rol}, _key.salat, {
                    expiresIn: 60 * 60 * 24
                });

                results.forEach(element => {
                    delete element.password;
                });

                jResponse = {
                    status: 200,
                    auth: true,
                    token: _token,
                    results: results
                }

            } else {
                jResponse = { status: 410, token: null, results :[] };
            }

            res.status(200).jsonp(jResponse);

        });
        
    } catch (e) {
        
        jResponse = {
            status: 500,
            results: e.message
        }
        res.status(200).jsonp(jResponse);

    } finally {}
}