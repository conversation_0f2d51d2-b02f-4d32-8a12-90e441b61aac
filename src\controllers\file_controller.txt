const S3 = require('./../utils/aws');
const formidable = require('formidable')
const ctrlModule = {};

ctrlModule.post = async (req, res) => {

    try {
        var form = new formidable.IncomingForm({
            keepExtensions: true
        });

        form.parse(req, (err, fields, files) => {
            S3.uploadFile(files).then((s3_resp) => {
                res.json({ status: 200, message: 'image saved', image_url : s3_resp.Location })
            }, (err) => {
                // console.log("->", err)
            });
        });

    } catch (error) {
        res.status(500).send(error);
    }

}

module.exports = ctrlModule;