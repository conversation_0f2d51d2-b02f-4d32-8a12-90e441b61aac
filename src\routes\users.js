var express = require('express');
const { getUsers, getUsersById, insertUser, updateUser, deleteUser } = require('../controllers/users_controller');

var Users = express.Router();

Users.route("/users")
    .get(getUsers);
Users.route("/users")
    .post(insertUser);
Users.route("/users/:id")
    .get(getUsersById);
Users.route("/users/:id")
    .put(updateUser);
 Users.route("/users/:id")
    .delete(deleteUser);

module.exports = Users;