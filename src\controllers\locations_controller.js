var { createConnection } = require('../database');
const _TABLE = 'zones';

exports.get = (req, res) => {

    sql = `SELECT * from ${ _TABLE }`;

    createConnection().query(sql, (error, result) => {

        let zones = result;
        let object_response = [];

        if (error) throw error;

        if(zones.length > 0){

            let _sql = 'SELECT * FROM zones as z, places as p where z.zone_number = p.zone_number;';
            createConnection().query(_sql, (error, _result) => {

                let join_result = _result;
                if (error) throw error;

                if(join_result.length > 0){
                
                    zones.forEach(zone => {
                        let zone_number = zone.zone_number;
                        let id_zone = zone.id_zone;
                        let obj_zone = {};
                        let obj_options = [];

                        obj_zone.label = zone.label;

                        join_result.forEach( place => {

                            if(place.zone_number == zone_number){
                                obj_options.push({
                                    "label": place.label,
                                    "value": place.label,
                                    "place": place.name,
                                    "zone": zone_number,
                                    "id_zone": id_zone
                                })
                            }

                        });

                        obj_zone.options = obj_options;
                        object_response.push(obj_zone);

                    });
                }
                res.status(200).json(object_response);
            })

        }else{
            res.status(200).json({message: 'no zones'});
        }

    });
}

