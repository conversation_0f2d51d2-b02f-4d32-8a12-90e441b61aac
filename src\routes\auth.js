var express = require('express');

//Controller-bd-action
var { login } = require('../controllers/auth_controller');
const verifyToken = require('../controllers/verifyToken');

var Auth = express.Router();

/** Users Routes */
Auth.route("/login")
    .post(login);
Auth.route("/verifytoken")
    .get(verifyToken,(req, res)=>{
        return res.status(200).json({
            status: 200,
            auth: true,
            id_user: req.userId,
        });
    })

module.exports = Auth;