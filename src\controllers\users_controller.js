var { createConnection } = require('../database');
var conn = createConnection();
const formidable = require('formidable');
const _utils = require('./../utils');
const crypto = require('crypto');
const _TABLE = 'users';
const _ID = 'id_user';

exports.getUsers = (req, res) => {

    try {

        sql = `SELECT * FROM users where status < 3`;

        conn.query(sql, function (error, results) {
            if (error) throw error;

            results.forEach(element => {
                delete element.password;
            });

            jResponse = {
                status: 200,
                results: results
            }
            res.status(200).json(jResponse);
        });

    } catch (e) {
        jResponse = {
            status: 500,
            results: e.message
        }
        res.status(200).json(jResponse);
    } finally { }
}

exports.getUsersById = (req, res) => {
    try {
        sql = "SELECT * FROM users where status < 3 and id_user = " + req.params.id;

        conn.query(sql, function (error, results) {
            if (error) throw error;
            if (results.length > 0) {

                results.forEach(element => {
                    delete element.password;
                });

                jResponse = { status: 200, results: results };

            } else {
                jResponse = { status: 404, results: [] };
            }
            res.status(200).json(jResponse);
        });
    } catch (e) {
        jResponse = {
            status: 500,
            results: e.message
        }
        res.status(200).json(jResponse);

    } finally { }
}

exports.insertUser = (req, res) => {

    var form = new formidable.IncomingForm({
        keepExtensions: true
    });

    form.parse(req, function (error, fields, files) {

        _utils.functionValidate([fields.id_rol, fields.fullname, fields.email, fields.password, fields.phone], function (validateResult) {

            if (validateResult) {

                try {

                    fields.password = crypto.createHash('md5').update(fields.password).digest("hex");
                    sql = `INSERT INTO ${_TABLE} SET ?`;

                    conn.query(sql, fields, function (errorinsert, resultsinsert, fieldsinsert) {

                        if (errorinsert) {
                            jResponse = { status: 500, resultsinsert: errorinsert }
                            res.status(500).json(jResponse);
                        } else {
                            jResponse = { status: 200, results: resultsinsert };
                            res.status(200).json(jResponse);
                        }

                    });

                } catch (e) {

                    jResponse = { status: 500, results: e.message }
                    res.status(500).json(jResponse);
                } finally { }

            } else {
                jResponse = { status: 405, results: 'datos incorrectos' }
                res.status(405).json(jResponse);
            }
        });
    });
}

exports.updateUser = (req, res) => {

    var form = new formidable.IncomingForm({
        keepExtensions: true
    });

    form.parse(req, function (error, fields, files) {

        _utils.functionValidateObj(fields, function (validateResult) {
            if (validateResult) {
                try {
                    fields.password = crypto.createHash('md5').update(fields.password).digest("hex");
                    sql = `UPDATE ${_TABLE} SET `;
                    let data = [];
                    let aux = 0;
                    Object.keys(fields).forEach((e) => {
                        sql += (aux > 0) ? ',' + e + '= ?' : e + '= ?';
                        aux++;
                        data.push(fields[e]);
                    });

                    sql += ` WHERE ${_ID} = ? `;
                    data.push(req.params.id);

                    conn.query(sql, data, function (error, results, fields) {

                        if (error) {
                            jResponse = { status: 500, results: error.code }
                            res.status(500).json(jResponse);
                        } else {
                            jResponse = { status: 200, results: results.insertId };
                            res.status(200).json(jResponse);
                        }

                    });

                } catch (e) {
                    jResponse = { status: 500, results: e.message }
                    res.status(500).json(jResponse);
                } finally { }

            } else {
                jResponse = { status: 405, results: 'datos incorrectos' }
                res.status(405).json(jResponse);
            }
        });
    });
}

exports.deleteUser = function (req, res) {
    try {
        sql = `update users SET status=3 where id_user= ${req.params.id}`;
        conn.query(sql, function (error, results, fields) {
            if (error) throw error;
            jResponse = {
                status: 200
            }
            res.status(200).json(jResponse);
        });
    } catch (e) {
        jResponse = {
            status: 500,
            results: e.message
        }
        res.status(200).json(jResponse);
    } finally { }
}