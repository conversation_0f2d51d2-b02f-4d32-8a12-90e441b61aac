var express = require('express');

const ctrl = require('../controllers/airlines_controller');

var Airlines = express.Router();

Airlines.route("/airlines")
    .get(ctrl.get);
Airlines.route("/airlines")
    .post(ctrl.insert);
Airlines.route("/airlines/:id")
    .get(ctrl.getById);
Airlines.route("/airlines/:id")
    .put(ctrl.update);
Airlines.route("/airlines/:id")
    .delete(ctrl._delete);

module.exports = Airlines;