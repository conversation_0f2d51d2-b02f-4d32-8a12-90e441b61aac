const jwt = require('jsonwebtoken');
const _key = require('./../utils');

function verifyToken (req, res,next) {

    const token = req.headers.authorization;
    if(!token){
        return res.status(401).json({
            auth: false,
            message: 'no token provided'
        });
    }
    try {
        const decoded = jwt.verify(token, _key.salat)
        req.userId = decoded.id;
        req.id_rol=decoded.id_rol;
        next();
    } catch (error) {
        return res.status(401).json({
            auth: false,
            message: 'no token provided'
        });
    }
    
};

module.exports = verifyToken;