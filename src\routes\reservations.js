var express = require('express');

const ctrl = require('../controllers/reservations_controller');

var Reservations = express.Router();

Reservations.route("/reservations")
    .get(ctrl.get);
Reservations.route("/reservations")
    .post(ctrl.insert);
Reservations.route("/reservations/:id")
    .get(ctrl.getByFolio);
Reservations.route("/reservations/:id")
    .put(ctrl.update);
Reservations.route("/reservations/:id")
    .delete(ctrl._delete);

module.exports = Reservations;